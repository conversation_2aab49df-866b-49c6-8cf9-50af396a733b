/**
 * SSH Client implementation using ssh2
 */
import { ClientChannel } from 'ssh2';
import { EventEmitter } from 'events';
import { SSHConnectionConfig, SSHConnectionState, SSHCommandOptions, SSHCommandResult, SSHTerminalConfig } from './types';
export interface SSHClientOptions {
    debug?: boolean;
    logger?: (level: string, message: string) => void;
    maxRetries?: number;
    retryDelay?: number;
}
export declare class SSHClient extends EventEmitter {
    private client;
    private config;
    private options;
    private state;
    private connectPromise;
    private retryCount;
    private activeChannels;
    constructor(config: SSHConnectionConfig, options?: SSHClientOptions);
    /**
     * Get current connection state
     */
    get connectionState(): SSHConnectionState;
    /**
     * Check if client is connected and ready
     */
    get isReady(): boolean;
    /**
     * Check if client is connected (any connected state)
     */
    get isConnected(): boolean;
    /**
     * Connect to SSH server
     */
    connect(): Promise<void>;
    /**
     * Disconnect from SSH server
     */
    disconnect(): Promise<void>;
    /**
     * Execute a command on the remote server
     */
    executeCommand(command: string, options?: SSHCommandOptions): Promise<SSHCommandResult>;
    /**
     * Create a shell session
     */
    createShell(terminalConfig?: SSHTerminalConfig): Promise<ClientChannel>;
    /**
     * Perform the actual connection
     */
    private performConnect;
    /**
     * Build ssh2 connection configuration
     */
    private buildConnectConfig;
    /**
     * Set up event handlers for the SSH client
     */
    private setupEventHandlers;
    /**
     * Set connection state and emit event
     */
    private setState;
    /**
     * Log message with appropriate level
     */
    private log;
}
//# sourceMappingURL=client.d.ts.map