{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,MAAM,EAAgC,MAAM,MAAM,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAQtC,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAC3E,OAAO,EAAE,iBAAiB,EAAE,wBAAwB,EAAE,MAAM,cAAc,CAAC;AAS3E,MAAM,OAAO,SAAU,SAAQ,YAAY;IASzC,YAAY,MAA2B,EAAE,UAA4B,EAAE;QACrE,KAAK,EAAE,CAAC;QANF,UAAK,GAAuB,cAAc,CAAC;QAC3C,mBAAc,GAAyB,IAAI,CAAC;QAC5C,eAAU,GAAG,CAAC,CAAC;QACf,mBAAc,GAAG,IAAI,GAAG,EAAiB,CAAC;QAKhD,yBAAyB;QACzB,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,cAAc,CAAC,8BAA8B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,mBACV,KAAK,EAAE,KAAK,EACZ,UAAU,EAAE,CAAC,EACb,UAAU,EAAE,IAAI,IACb,OAAO,CACX,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,0BAA0B,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YAC1F,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,KAAK,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,CAAC,WAAW,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAE5C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,CAAC;QAC5B,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,KAAK,KAAK,cAAc,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,0BAA0B,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAE9B,4BAA4B;QAC5B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,0BAA0B,KAAK,EAAE,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAElB,mCAAmC;QACnC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,UAA6B,EAAE;QACnE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,QAAQ,GAAkB,IAAI,CAAC;YACnC,IAAI,MAA0B,CAAC;YAE/B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,qBAAqB;YAC/D,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,MAAM,CAAC,cAAc,CAAC,qCAAqC,OAAO,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC;YAC9F,CAAC,EAAE,OAAO,CAAC,CAAC;YAEZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACjD,IAAI,GAAG,EAAE,CAAC;oBACR,YAAY,CAAC,SAAS,CAAC,CAAC;oBACxB,MAAM,CAAC,cAAc,CAAC,6BAA6B,GAAG,CAAC,OAAO,EAAE,EAAE,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;oBACzF,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAEhC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,UAAkB,EAAE,EAAE;oBACtD,YAAY,CAAC,SAAS,CAAC,CAAC;oBACxB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAEnC,QAAQ,GAAG,IAAI,CAAC;oBAChB,MAAM,GAAG,UAAU,CAAC;oBAEpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;oBACxC,MAAM,MAAM,GAAqB;wBAC/B,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,MAAM;wBACN,OAAO,EAAE,QAAQ,KAAK,CAAC;wBACvB,QAAQ;qBACT,CAAC;oBAEF,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,qBAAqB,OAAO,gBAAgB,QAAQ,eAAe,QAAQ,KAAK,CAAC,CAAC;oBACpG,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;oBACjC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;oBACxC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;oBAClC,YAAY,CAAC,SAAS,CAAC,CAAC;oBACxB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACnC,MAAM,CAAC,cAAc,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;gBAClF,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,iBAAoC,EAAE;QACtD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,cAAc,CAAC,IAAI,IAAI,EAAE;gBAC/B,IAAI,EAAE,cAAc,CAAC,IAAI,IAAI,EAAE;gBAC/B,IAAI,EAAE,cAAc,CAAC,IAAI,IAAI,gBAAgB;gBAC7C,GAAG,EAAE,cAAc,CAAC,GAAG,IAAI,EAAE;aAC9B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBAC9C,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,cAAc,CAAC,0BAA0B,GAAG,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC;oBACpF,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAEhC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBACtB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,uCAAuC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gBACzF,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE5B,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAEnC,6DAA6D;YAC7D,MAAM,OAAO,GAAG,GAAG,EAAE;gBACnB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACvB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;gBACpB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC;gBAC1C,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;YAEF,MAAM,OAAO,GAAG,KAAK,EAAE,KAAY,EAAE,EAAE;gBACrC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAE5D,MAAM,QAAQ,GAAG,cAAc,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBAEjG,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAW,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/E,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,gCAAgC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;oBAEhG,oBAAoB;oBACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,UAAW,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;oBAE9F,yCAAyC;oBACzC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAC7C,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAE7C,IAAI,CAAC;wBACH,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;wBAC5B,OAAO,EAAE,CAAC;oBACZ,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,MAAM,CAAC,UAAU,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACvB,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,MAAM,MAAM,GAAQ;YAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;YAC5B,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;YACnC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,KAAK;YAC/C,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,KAAK;YACzD,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC;SACtD,CAAC;QAEF,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACvB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC3B,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAC7C,CAAC;QAED,4BAA4B;QAC5B,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC9B,KAAK,UAAU;gBACb,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC5C,MAAM;YACR,KAAK,KAAK;gBACR,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAChD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAClD,CAAC;gBACD,MAAM;YACR,KAAK,OAAO;gBACV,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;gBACtE,IAAI,SAAS,EAAE,CAAC;oBACd,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;gBAC3B,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACvC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvB,MAAM,QAAQ,GAAG,cAAc,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACvG,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAC/F,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,QAA4B;QAC3C,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,sBAAsB,QAAQ,OAAO,QAAQ,EAAE,CAAC,CAAC;YACnE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,KAAa,EAAE,OAAe;QACxC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;CACF"}