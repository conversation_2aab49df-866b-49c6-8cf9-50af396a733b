/**
 * SSH Connection Manager with pooling and reconnection logic
 */
import { EventEmitter } from 'events';
import { SSHClient } from './client';
import { SSHConnectionConfig, SSHConnectionPoolConfig, SSHConnectionPoolStats, SSHReconnectionConfig } from './types';
export interface ConnectionManagerOptions {
    poolConfig?: SSHConnectionPoolConfig;
    reconnectionConfig?: SSHReconnectionConfig;
    debug?: boolean;
    logger?: (level: string, message: string) => void;
}
export declare class SSHConnectionManager extends EventEmitter {
    private connections;
    private poolConfig;
    private reconnectionConfig;
    private options;
    private cleanupInterval;
    private nextConnectionId;
    constructor(options?: ConnectionManagerOptions);
    /**
     * Get or create a connection
     */
    getConnection(config: SSHConnectionConfig): Promise<SSHClient>;
    /**
     * Release a connection back to the pool
     */
    releaseConnection(client: SSHClient): void;
    /**
     * Close a specific connection
     */
    closeConnection(client: SSHClient): Promise<void>;
    /**
     * Close all connections
     */
    closeAllConnections(): Promise<void>;
    /**
     * Get connection pool statistics
     */
    getStats(): SSHConnectionPoolStats;
    /**
     * Create a new pooled connection
     */
    private createConnection;
    /**
     * Set up automatic reconnection for a connection
     */
    private setupReconnectionHandling;
    /**
     * Calculate reconnection delay with exponential backoff and jitter
     */
    private calculateReconnectionDelay;
    /**
     * Find an idle connection for the given configuration
     */
    private findIdleConnection;
    /**
     * Find connection by client instance
     */
    private findConnectionByClient;
    /**
     * Generate a unique key for connection configuration
     */
    private getConnectionKey;
    /**
     * Destroy a pooled connection
     */
    private destroyConnection;
    /**
     * Clean up idle connections
     */
    private cleanupIdleConnections;
    /**
     * Start the cleanup timer
     */
    private startCleanupTimer;
    /**
     * Log message
     */
    private log;
}
//# sourceMappingURL=connection-manager.d.ts.map