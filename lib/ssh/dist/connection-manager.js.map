{"version": 3, "file": "connection-manager.js", "sourceRoot": "", "sources": ["../src/connection-manager.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAOrC,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,UAAU,CAAC;AAmB9D,MAAM,OAAO,oBAAqB,SAAQ,YAAY;IAQpD,YAAY,UAAoC,EAAE;QAChD,KAAK,EAAE,CAAC;QARF,gBAAW,GAAG,IAAI,GAAG,EAA4B,CAAC;QAIlD,oBAAe,GAA0B,IAAI,CAAC;QAC9C,qBAAgB,GAAG,CAAC,CAAC;QAK3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,UAAU,mBACb,cAAc,EAAE,EAAE,EAClB,WAAW,EAAE,MAAM,EACnB,cAAc,EAAE,KAAK,EACrB,aAAa,EAAE,CAAC,EAChB,UAAU,EAAE,IAAI,IACb,OAAO,CAAC,UAAU,CACtB,CAAC;QAEF,IAAI,CAAC,kBAAkB,mBACrB,OAAO,EAAE,IAAI,EACb,WAAW,EAAE,CAAC,EACd,YAAY,EAAE,IAAI,EAClB,QAAQ,EAAE,KAAK,EACf,aAAa,EAAE,CAAC,EAChB,MAAM,EAAE,IAAI,IACT,OAAO,CAAC,kBAAkB,CAC9B,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAA2B;QAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEpD,0CAA0C;QAC1C,IAAI,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAE9D,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC;YAC9B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YACvC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,gCAAgC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;YACzE,OAAO,gBAAgB,CAAC,MAAM,CAAC;QACjC,CAAC;QAED,0CAA0C;QAC1C,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAC5D,mCAAmC;YACnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;gBAC5D,MAAM,cAAc,CAClB,mCAAmC,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,EACpE,gBAAgB,CACjB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAE5D,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,2BAA2B,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;QACpE,OAAO,gBAAgB,CAAC,MAAM,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAiB;QACjC,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAE7D,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YACvC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,wBAAwB,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,MAAiB;QACrC,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAE7D,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAC/C,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,sBAAsB,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAC7D,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CACjD,CAAC;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACjC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACjF,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC;QAE5B,OAAO;YACL,KAAK;YACL,MAAM;YACN,IAAI;YACJ,OAAO,EAAE,CAAC,CAAC,kCAAkC;SAC9C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAA2B;QACxD,MAAM,EAAE,GAAG,OAAO,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;QAC5C,MAAM,aAAa,GAAQ,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YACrC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC3C,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACtC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC7C,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAEpD,MAAM,gBAAgB,GAAqB;YACzC,EAAE;YACF,MAAM;YACN,MAAM;YACN,OAAO,EAAE,IAAI,IAAI,EAAE;YACnB,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,KAAK,EAAE,IAAI;YACX,iBAAiB,EAAE,CAAC;SACrB,CAAC;QAEF,+BAA+B;QAC/B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;QACnD,CAAC;QAED,qBAAqB;QACrB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QAEvB,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,gBAAkC;QAClE,MAAM,EAAE,MAAM,EAAE,GAAG,gBAAgB,CAAC;QAEpC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBACnE,OAAO;YACT,CAAC;YAED,IAAI,gBAAgB,CAAC,iBAAiB,IAAI,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;gBAC9E,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,yCAAyC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;gBAClF,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;gBAC/C,OAAO;YACT,CAAC;YAED,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;YAElF,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,gBAAgB,gBAAgB,CAAC,EAAE,OAAO,KAAK,eAAe,gBAAgB,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAEtH,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,IAAI,CAAC;oBACH,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;oBACvB,gBAAgB,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBACvC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,4BAA4B,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;gBACtE,CAAC;gBAAC,OAAO,cAAc,EAAE,CAAC;oBACxB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,2BAA2B,gBAAgB,CAAC,EAAE,KAAK,cAAc,EAAE,CAAC,CAAC;gBACzF,CAAC;YACH,CAAC,EAAE,KAAK,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACtB,IAAI,gBAAgB,CAAC,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBAC9D,kDAAkD;gBAClD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,cAAc,gBAAgB,CAAC,EAAE,sBAAsB,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,OAAe;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QAClD,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;QAE5D,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEjF,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;YACnC,2BAA2B;YAC3B,MAAM,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACtD,KAAK,IAAI,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,aAAqB;QAC9C,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,UAAU,CAAC,KAAK;gBACjB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,aAAa;gBAC1D,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC9B,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,MAAiB;QAC9C,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YACnD,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACjC,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAA2B;QAClD,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,gBAAkC;QAChE,IAAI,CAAC;YACH,MAAM,gBAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,uBAAuB,gBAAgB,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,mBAAmB,GAAuB,EAAE,CAAC;QAEnD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC/D,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;oBAC3C,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;QACH,CAAC;QAED,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACvC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,gCAAgC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;YACnE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,2BAA2B;QAC3B,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,KAAa,EAAE,OAAe;QACxC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,uBAAuB,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;CACF"}