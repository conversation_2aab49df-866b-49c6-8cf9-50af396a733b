/**
 * SSH Error handling utilities
 */
import { SSHError } from './types';
export declare const SSH_ERROR_CODES: {
    readonly CONNECTION_REFUSED: "ECONNREFUSED";
    readonly CONNECTION_TIMEOUT: "ETIMEDOUT";
    readonly HOST_UNREACHABLE: "EHOSTUNREACH";
    readonly NETWORK_UNREACHABLE: "ENETUNREACH";
    readonly AUTH_FAILED: "AUTH_FAILED";
    readonly AUTH_PARTIAL: "AUTH_PARTIAL";
    readonly AUTH_METHOD_NOT_SUPPORTED: "AUTH_METHOD_NOT_SUPPORTED";
    readonly PROTOCOL_ERROR: "PROTOCOL_ERROR";
    readonly HANDSHAKE_FAILED: "HANDSHAKE_FAILED";
    readonly COMMAND_FAILED: "COMMAND_FAILED";
    readonly COMMAND_TIMEOUT: "COMMAND_TIMEOUT";
    readonly STREAM_ERROR: "STREAM_ERROR";
    readonly STREAM_CLOSED: "STREAM_CLOSED";
    readonly INVALID_CONFIG: "INVALID_CONFIG";
    readonly INVALID_HOST: "INVALID_HOST";
    readonly INVALID_PORT: "INVALID_PORT";
    readonly INVALID_AUTH: "INVALID_AUTH";
    readonly INVALID_KEY: "INVALID_KEY";
    readonly KEY_NOT_FOUND: "KEY_NOT_FOUND";
    readonly KEY_PASSPHRASE_REQUIRED: "KEY_PASSPHRASE_REQUIRED";
    readonly HOST_KEY_MISMATCH: "HOST_KEY_MISMATCH";
    readonly HOST_KEY_UNKNOWN: "HOST_KEY_UNKNOWN";
    readonly UNKNOWN_ERROR: "UNKNOWN_ERROR";
    readonly OPERATION_ABORTED: "OPERATION_ABORTED";
};
export type SSHErrorCode = typeof SSH_ERROR_CODES[keyof typeof SSH_ERROR_CODES];
/**
 * Create appropriate SSH error from raw error
 */
export declare function createSSHError(message: string, code?: string, details?: any): SSHError;
export declare function createSSHError(error: any, context?: string): SSHError;
/**
 * Check if error is recoverable (can retry)
 */
export declare function isRecoverableError(error: SSHError): boolean;
/**
 * Check if error is authentication-related
 */
export declare function isAuthenticationError(error: SSHError): boolean;
/**
 * Check if error is connection-related
 */
export declare function isConnectionError(error: SSHError): boolean;
/**
 * Get user-friendly error message
 */
export declare function getUserFriendlyErrorMessage(error: SSHError): string;
/**
 * Error recovery suggestions
 */
export declare function getErrorRecoverySuggestions(error: SSHError): string[];
/**
 * Log error with appropriate level
 */
export declare function logSSHError(error: SSHError, logger?: (level: string, message: string) => void): void;
//# sourceMappingURL=errors.d.ts.map