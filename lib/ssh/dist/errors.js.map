{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EACL,QAAQ,EACR,kBAAkB,EAClB,sBAAsB,EACtB,eAAe,EACf,eAAe,EAChB,MAAM,SAAS,CAAC;AAEjB,sBAAsB;AACtB,MAAM,CAAC,MAAM,eAAe,GAAG;IAC7B,oBAAoB;IACpB,kBAAkB,EAAE,cAAc;IAClC,kBAAkB,EAAE,WAAW;IAC/B,gBAAgB,EAAE,cAAc;IAChC,mBAAmB,EAAE,aAAa;IAElC,wBAAwB;IACxB,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,cAAc;IAC5B,yBAAyB,EAAE,2BAA2B;IAEtD,kBAAkB;IAClB,cAAc,EAAE,gBAAgB;IAChC,gBAAgB,EAAE,kBAAkB;IAEpC,iBAAiB;IACjB,cAAc,EAAE,gBAAgB;IAChC,eAAe,EAAE,iBAAiB;IAElC,gBAAgB;IAChB,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,eAAe;IAE9B,uBAAuB;IACvB,cAAc,EAAE,gBAAgB;IAChC,YAAY,EAAE,cAAc;IAC5B,YAAY,EAAE,cAAc;IAC5B,YAAY,EAAE,cAAc;IAE5B,aAAa;IACb,WAAW,EAAE,aAAa;IAC1B,aAAa,EAAE,eAAe;IAC9B,uBAAuB,EAAE,yBAAyB;IAElD,2BAA2B;IAC3B,iBAAiB,EAAE,mBAAmB;IACtC,gBAAgB,EAAE,kBAAkB;IAEpC,iBAAiB;IACjB,aAAa,EAAE,eAAe;IAC9B,iBAAiB,EAAE,mBAAmB;CAC9B,CAAC;AASX,MAAM,UAAU,cAAc,CAAC,cAAmB,EAAE,aAAsB,EAAE,OAAa;IACvF,wCAAwC;IACxC,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;QACvC,0DAA0D;QAC1D,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,aAAa,IAAI,eAAe,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;IAED,mDAAmD;IACnD,MAAM,KAAK,GAAG,cAAc,CAAC;IAC7B,MAAM,OAAO,GAAG,aAAa,CAAC;IAC9B,MAAM,OAAO,GAAG,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,KAAI,mBAAmB,CAAC;IACtD,MAAM,IAAI,GAAG,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,KAAK,CAAA,IAAI,eAAe,CAAC;IAE5D,4BAA4B;IAC5B,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;QAC7E,OAAO,IAAI,kBAAkB,CAC3B,sBAAsB,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAChE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,CAC/B,CAAC;IACJ,CAAC;IAED,iBAAiB;IACjB,IAAI,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACxD,OAAO,IAAI,eAAe,CACxB,wBAAwB,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAClE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,CAC/B,CAAC;IACJ,CAAC;IAED,wBAAwB;IACxB,IAAI,IAAI,KAAK,aAAa,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;QACjE,OAAO,IAAI,sBAAsB,CAC/B,0BAA0B,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACpE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,CAC/B,CAAC;IACJ,CAAC;IAED,2BAA2B;IAC3B,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,MAAK,SAAS,EAAE,CAAC;QAClC,OAAO,IAAI,eAAe,CACxB,mBAAmB,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAC7D,KAAK,CAAC,QAAQ,EACd,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,CAC/B,CAAC;IACJ,CAAC;IAED,oBAAoB;IACpB,OAAO,IAAI,QAAQ,CACjB,cAAc,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACxD,IAAI,EACJ,EAAE,aAAa,EAAE,KAAK,EAAE,CACzB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAe;IAChD,MAAM,gBAAgB,GAAa;QACjC,eAAe,CAAC,kBAAkB;QAClC,eAAe,CAAC,kBAAkB;QAClC,eAAe,CAAC,mBAAmB;QACnC,eAAe,CAAC,eAAe;KAChC,CAAC;IAEF,OAAO,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC/C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAe;IACnD,OAAO,KAAK,YAAY,sBAAsB;QACvC,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,WAAW;QAC1C,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,YAAY;QAC3C,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,yBAAyB,CAAC;AAClE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAe;IAC/C,OAAO,KAAK,YAAY,kBAAkB;QACnC,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,kBAAkB;QACjD,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,kBAAkB;QACjD,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,gBAAgB;QAC/C,KAAK,CAAC,IAAI,KAAK,eAAe,CAAC,mBAAmB,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B,CAAC,KAAe;IACzD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,eAAe,CAAC,kBAAkB;YACrC,OAAO,kEAAkE,CAAC;QAE5E,KAAK,eAAe,CAAC,kBAAkB;YACrC,OAAO,oEAAoE,CAAC;QAE9E,KAAK,eAAe,CAAC,WAAW;YAC9B,OAAO,qEAAqE,CAAC;QAE/E,KAAK,eAAe,CAAC,gBAAgB;YACnC,OAAO,4DAA4D,CAAC;QAEtE,KAAK,eAAe,CAAC,WAAW;YAC9B,OAAO,wDAAwD,CAAC;QAElE,KAAK,eAAe,CAAC,uBAAuB;YAC1C,OAAO,+DAA+D,CAAC;QAEzE,KAAK,eAAe,CAAC,iBAAiB;YACpC,OAAO,mEAAmE,CAAC;QAE7E,KAAK,eAAe,CAAC,eAAe;YAClC,OAAO,8BAA8B,CAAC;QAExC;YACE,OAAO,KAAK,CAAC,OAAO,IAAI,4BAA4B,CAAC;IACzD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B,CAAC,KAAe;IACzD,MAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,eAAe,CAAC,kBAAkB;YACrC,WAAW,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAChE,WAAW,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACtE,WAAW,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAC3D,MAAM;QAER,KAAK,eAAe,CAAC,kBAAkB;YACrC,WAAW,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACnD,WAAW,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC1D,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACxD,MAAM;QAER,KAAK,eAAe,CAAC,WAAW;YAC9B,WAAW,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YACrE,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACxD,MAAM;QAER,KAAK,eAAe,CAAC,iBAAiB;YACpC,WAAW,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC7D,WAAW,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YACjE,WAAW,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACtD,MAAM;QAER,KAAK,eAAe,CAAC,WAAW;YAC9B,WAAW,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAClE,WAAW,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACzD,WAAW,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAClE,MAAM;QAER;YACE,WAAW,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YACjE,WAAW,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACzD,MAAM;IACV,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,KAAe,EAAE,MAAiD;IAC5F,MAAM,GAAG,GAAG,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC;IAEpC,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,GAAG,CAAC,MAAM,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAC5D,CAAC;SAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QACpC,GAAG,CAAC,OAAO,EAAE,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACzD,CAAC;SAAM,IAAI,KAAK,YAAY,eAAe,EAAE,CAAC;QAC5C,GAAG,CAAC,MAAM,EAAE,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACrD,CAAC;SAAM,CAAC;QACN,GAAG,CAAC,OAAO,EAAE,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAClB,GAAG,CAAC,OAAO,EAAE,kBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3E,CAAC;AACH,CAAC"}