/**
 * React hook for SSH connections
 */
import { SSHClient } from '../client';
import { SSHConnectionConfig, SSHConnectionState, SSHCommandOptions, SSHCommandResult } from '../types';
export interface UseSSHConnectionOptions {
    autoConnect?: boolean;
    retryAttempts?: number;
    retryDelay?: number;
    onStateChange?: (state: SSHConnectionState) => void;
    onError?: (error: Error) => void;
    onConnect?: () => void;
    onDisconnect?: () => void;
}
export interface UseSSHConnectionReturn {
    client: SSHClient | null;
    state: SSHConnectionState;
    isConnected: boolean;
    isReady: boolean;
    error: Error | null;
    connect: () => Promise<void>;
    disconnect: () => Promise<void>;
    reconnect: () => Promise<void>;
    executeCommand: (command: string, options?: SSHCommandOptions) => Promise<SSHCommandResult>;
    connectionInfo: {
        host: string;
        port: number;
        username: string;
        connectedAt: Date | null;
        lastActivity: Date | null;
    } | null;
}
export declare function useSSHConnection(config: SSHConnectionConfig, options?: UseSSHConnectionOptions): UseSSHConnectionReturn;
//# sourceMappingURL=use-ssh-connection.d.ts.map