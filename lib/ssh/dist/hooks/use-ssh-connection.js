/**
 * React hook for SSH connections
 */
import { useState, useEffect, useRef, useCallback } from 'react';
import { SSHConnectionManager } from '../connection-manager';
import { createSSHError } from '../errors';
// Global connection manager instance
let globalConnectionManager = null;
function getConnectionManager() {
    if (!globalConnectionManager) {
        globalConnectionManager = new SSHConnectionManager({
            debug: process.env.NODE_ENV === 'development'
        });
    }
    return globalConnectionManager;
}
export function useSSHConnection(config, options = {}) {
    const [client, setClient] = useState(null);
    const [state, setState] = useState('disconnected');
    const [error, setError] = useState(null);
    const [connectionInfo, setConnectionInfo] = useState(null);
    const connectionManager = useRef(getConnectionManager());
    const connectPromise = useRef(null);
    const retryCount = useRef(0);
    const lastActivity = useRef(null);
    const { autoConnect = false, retryAttempts = 3, retryDelay = 1000, onStateChange, onError, onConnect, onDisconnect } = options;
    // Update connection info when client changes
    useEffect(() => {
        if (client && state === 'ready') {
            setConnectionInfo({
                host: config.host,
                port: config.port || 22,
                username: config.auth.username,
                connectedAt: new Date(),
                lastActivity: lastActivity.current
            });
        }
        else {
            setConnectionInfo(null);
        }
    }, [client, state, config]);
    // Set up client event handlers
    useEffect(() => {
        if (!client)
            return;
        const handleStateChange = (newState) => {
            setState(newState);
            onStateChange === null || onStateChange === void 0 ? void 0 : onStateChange(newState);
        };
        const handleError = (err) => {
            setError(err);
            onError === null || onError === void 0 ? void 0 : onError(err);
        };
        const handleConnect = () => {
            setError(null);
            retryCount.current = 0;
            onConnect === null || onConnect === void 0 ? void 0 : onConnect();
        };
        const handleDisconnect = () => {
            onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect();
        };
        client.on('stateChange', handleStateChange);
        client.on('error', handleError);
        client.on('ready', handleConnect);
        client.on('close', handleDisconnect);
        client.on('end', handleDisconnect);
        return () => {
            client.removeListener('stateChange', handleStateChange);
            client.removeListener('error', handleError);
            client.removeListener('ready', handleConnect);
            client.removeListener('close', handleDisconnect);
            client.removeListener('end', handleDisconnect);
        };
    }, [client, onStateChange, onError, onConnect, onDisconnect]);
    // Connect function
    const connect = useCallback(async () => {
        if (connectPromise.current) {
            return connectPromise.current;
        }
        if (client === null || client === void 0 ? void 0 : client.isConnected) {
            return;
        }
        connectPromise.current = (async () => {
            try {
                setError(null);
                setState('connecting');
                const newClient = await connectionManager.current.getConnection(config);
                setClient(newClient);
                if (!newClient.isReady) {
                    await newClient.connect();
                }
                lastActivity.current = new Date();
            }
            catch (err) {
                const sshError = createSSHError(`Failed to connect: ${err}`, 'CONNECTION_ERROR');
                setError(sshError);
                setState('error');
                // Retry logic
                if (retryCount.current < retryAttempts) {
                    retryCount.current++;
                    setTimeout(() => {
                        connectPromise.current = null;
                        connect();
                    }, retryDelay * retryCount.current);
                }
                throw sshError;
            }
            finally {
                connectPromise.current = null;
            }
        })();
        return connectPromise.current;
    }, [config, retryAttempts, retryDelay]);
    // Disconnect function
    const disconnect = useCallback(async () => {
        if (!client)
            return;
        try {
            connectionManager.current.releaseConnection(client);
            setClient(null);
            setState('disconnected');
            setError(null);
            lastActivity.current = null;
        }
        catch (err) {
            const sshError = createSSHError(`Failed to disconnect: ${err}`, 'DISCONNECTION_ERROR');
            setError(sshError);
            throw sshError;
        }
    }, [client]);
    // Reconnect function
    const reconnect = useCallback(async () => {
        await disconnect();
        await connect();
    }, [disconnect, connect]);
    // Execute command function
    const executeCommand = useCallback(async (command, commandOptions = {}) => {
        if (!client) {
            throw createSSHError('No SSH connection available', 'NO_CONNECTION');
        }
        try {
            lastActivity.current = new Date();
            const result = await client.executeCommand(command, commandOptions);
            lastActivity.current = new Date();
            return result;
        }
        catch (err) {
            const sshError = createSSHError(`Command execution failed: ${err}`, 'COMMAND_ERROR');
            setError(sshError);
            throw sshError;
        }
    }, [client]);
    // Auto-connect on mount
    useEffect(() => {
        if (autoConnect) {
            connect().catch(() => {
                // Error is already handled in connect function
            });
        }
        // Cleanup on unmount
        return () => {
            if (client) {
                connectionManager.current.releaseConnection(client);
            }
        };
    }, [autoConnect, connect]);
    // Derived state
    const isConnected = state !== 'disconnected' && state !== 'error';
    const isReady = state === 'ready';
    return {
        client,
        state,
        isConnected,
        isReady,
        error,
        connect,
        disconnect,
        reconnect,
        executeCommand,
        connectionInfo
    };
}
//# sourceMappingURL=use-ssh-connection.js.map