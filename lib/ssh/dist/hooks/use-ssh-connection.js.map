{"version": 3, "file": "use-ssh-connection.js", "sourceRoot": "", "sources": ["../../src/hooks/use-ssh-connection.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAEjE,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAO7D,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAsC3C,qCAAqC;AACrC,IAAI,uBAAuB,GAAgC,IAAI,CAAC;AAEhE,SAAS,oBAAoB;IAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7B,uBAAuB,GAAG,IAAI,oBAAoB,CAAC;YACjD,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;SAC9C,CAAC,CAAC;IACL,CAAC;IACD,OAAO,uBAAuB,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,MAA2B,EAC3B,UAAmC,EAAE;IAErC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAmB,IAAI,CAAC,CAAC;IAC7D,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAqB,cAAc,CAAC,CAAC;IACvE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAe,IAAI,CAAC,CAAC;IACvD,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAA2C,IAAI,CAAC,CAAC;IAErG,MAAM,iBAAiB,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC;IACzD,MAAM,cAAc,GAAG,MAAM,CAAuB,IAAI,CAAC,CAAC;IAC1D,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B,MAAM,YAAY,GAAG,MAAM,CAAc,IAAI,CAAC,CAAC;IAE/C,MAAM,EACJ,WAAW,GAAG,KAAK,EACnB,aAAa,GAAG,CAAC,EACjB,UAAU,GAAG,IAAI,EACjB,aAAa,EACb,OAAO,EACP,SAAS,EACT,YAAY,EACb,GAAG,OAAO,CAAC;IAEZ,6CAA6C;IAC7C,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,MAAM,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;YAChC,iBAAiB,CAAC;gBAChB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;gBACvB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ;gBAC9B,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,YAAY,EAAE,YAAY,CAAC,OAAO;aACnC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAE5B,+BAA+B;IAC/B,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,iBAAiB,GAAG,CAAC,QAA4B,EAAE,EAAE;YACzD,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAG,QAAQ,CAAC,CAAC;QAC5B,CAAC,CAAC;QAEF,MAAM,WAAW,GAAG,CAAC,GAAU,EAAE,EAAE;YACjC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACd,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC;QAEF,MAAM,aAAa,GAAG,GAAG,EAAE;YACzB,QAAQ,CAAC,IAAI,CAAC,CAAC;YACf,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC;YACvB,SAAS,aAAT,SAAS,uBAAT,SAAS,EAAI,CAAC;QAChB,CAAC,CAAC;QAEF,MAAM,gBAAgB,GAAG,GAAG,EAAE;YAC5B,YAAY,aAAZ,YAAY,uBAAZ,YAAY,EAAI,CAAC;QACnB,CAAC,CAAC;QAEF,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;QAC5C,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAChC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAClC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACrC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAEnC,OAAO,GAAG,EAAE;YACV,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;YACxD,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAC9C,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACjD,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QACjD,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;IAE9D,mBAAmB;IACnB,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,IAAmB,EAAE;QACpD,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,OAAO,cAAc,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,cAAc,CAAC,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE;YACnC,IAAI,CAAC;gBACH,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACf,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAEvB,MAAM,SAAS,GAAG,MAAM,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACxE,SAAS,CAAC,SAAS,CAAC,CAAC;gBAErB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACvB,MAAM,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC5B,CAAC;gBAED,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAEpC,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,QAAQ,GAAG,cAAc,CAC7B,sBAAsB,GAAG,EAAE,EAC3B,kBAAkB,CACnB,CAAC;gBAEF,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACnB,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAElB,cAAc;gBACd,IAAI,UAAU,CAAC,OAAO,GAAG,aAAa,EAAE,CAAC;oBACvC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACrB,UAAU,CAAC,GAAG,EAAE;wBACd,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC;wBAC9B,OAAO,EAAE,CAAC;oBACZ,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;gBACtC,CAAC;gBAED,MAAM,QAAQ,CAAC;YACjB,CAAC;oBAAS,CAAC;gBACT,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC;YAChC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,OAAO,cAAc,CAAC,OAAO,CAAC;IAChC,CAAC,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;IAExC,sBAAsB;IACtB,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,IAAmB,EAAE;QACvD,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,CAAC;YACH,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACpD,SAAS,CAAC,IAAI,CAAC,CAAC;YAChB,QAAQ,CAAC,cAAc,CAAC,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC,CAAC;YACf,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,cAAc,CAC7B,yBAAyB,GAAG,EAAE,EAC9B,qBAAqB,CACtB,CAAC;YACF,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,MAAM,QAAQ,CAAC;QACjB,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,qBAAqB;IACrB,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,IAAmB,EAAE;QACtD,MAAM,UAAU,EAAE,CAAC;QACnB,MAAM,OAAO,EAAE,CAAC;IAClB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;IAE1B,2BAA2B;IAC3B,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,EACtC,OAAe,EACf,iBAAoC,EAAE,EACX,EAAE;QAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,cAAc,CAAC,6BAA6B,EAAE,eAAe,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACpE,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAClC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,QAAQ,GAAG,cAAc,CAC7B,6BAA6B,GAAG,EAAE,EAClC,eAAe,CAChB,CAAC;YACF,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,MAAM,QAAQ,CAAC;QACjB,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,wBAAwB;IACxB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;gBACnB,+CAA+C;YACjD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,qBAAqB;QACrB,OAAO,GAAG,EAAE;YACV,IAAI,MAAM,EAAE,CAAC;gBACX,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IAE3B,gBAAgB;IAChB,MAAM,WAAW,GAAG,KAAK,KAAK,cAAc,IAAI,KAAK,KAAK,OAAO,CAAC;IAClE,MAAM,OAAO,GAAG,KAAK,KAAK,OAAO,CAAC;IAElC,OAAO;QACL,MAAM;QACN,KAAK;QACL,WAAW;QACX,OAAO;QACP,KAAK;QACL,OAAO;QACP,UAAU;QACV,SAAS;QACT,cAAc;QACd,cAAc;KACf,CAAC;AACJ,CAAC"}