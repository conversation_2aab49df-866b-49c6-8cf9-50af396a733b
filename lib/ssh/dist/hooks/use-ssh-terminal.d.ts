/**
 * React hook for SSH terminal integration
 */
import { SSHTerminal, SSHTerminalOptions } from '../terminal';
import { SSHClient } from '../client';
import { SSHTerminalConfig } from '../types';
export interface UseSSHTerminalOptions extends SSHTerminalOptions {
    autoConnect?: boolean;
    autoResize?: boolean;
    onData?: (data: string) => void;
    onResize?: (cols: number, rows: number) => void;
    onKey?: (key: string, event: KeyboardEvent) => void;
    onSelection?: (text: string) => void;
    onTitle?: (title: string) => void;
    onBell?: () => void;
    onError?: (error: Error) => void;
}
export interface UseSSHTerminalReturn {
    terminal: SSHTerminal | null;
    isAttached: boolean;
    isConnected: boolean;
    dimensions: {
        cols: number;
        rows: number;
    } | null;
    attach: (container: HTMLElement) => void;
    detach: () => void;
    connect: (terminalConfig?: SSHTerminalConfig) => Promise<void>;
    disconnect: () => Promise<void>;
    write: (data: string) => void;
    writeln: (data: string) => void;
    clear: () => void;
    reset: () => void;
    fit: () => void;
    resize: (cols: number, rows: number) => void;
    focus: () => void;
    getSelection: () => string;
    selectAll: () => void;
    copySelection: () => Promise<void>;
    paste: () => Promise<void>;
    error: Error | null;
}
export declare function useSSHTerminal(sshClient: SSHClient | null, options?: UseSSHTerminalOptions): UseSSHTerminalReturn;
//# sourceMappingURL=use-ssh-terminal.d.ts.map