/**
 * React hook for SSH terminal integration
 */
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import { useState, useEffect, useRef, useCallback } from 'react';
import { SSHTerminal } from '../terminal';
import { createSSHError } from '../errors';
export function useSSHTerminal(sshClient, options = {}) {
    const [terminal, setTerminal] = useState(null);
    const [isAttached, setIsAttached] = useState(false);
    const [isConnected, setIsConnected] = useState(false);
    const [dimensions, setDimensions] = useState(null);
    const [error, setError] = useState(null);
    const containerRef = useRef(null);
    const resizeObserverRef = useRef(null);
    const { autoConnect = false, autoResize = true, onData, onResize, onKey, onSelection, onTitle, onBell, onError } = options, terminalOptions = __rest(options, ["autoConnect", "autoResize", "onData", "onResize", "onKey", "onSelection", "onTitle", "onBell", "onError"]);
    // Create terminal instance when SSH client is available
    useEffect(() => {
        if (sshClient && !terminal) {
            try {
                const newTerminal = new SSHTerminal(sshClient, terminalOptions);
                setTerminal(newTerminal);
                setError(null);
            }
            catch (err) {
                const terminalError = createSSHError(`Failed to create terminal: ${err}`, 'TERMINAL_CREATION_ERROR');
                setError(terminalError);
                onError === null || onError === void 0 ? void 0 : onError(terminalError);
            }
        }
        else if (!sshClient && terminal) {
            // Clean up terminal when SSH client is removed
            terminal.detach();
            setTerminal(null);
            setIsAttached(false);
            setIsConnected(false);
            setDimensions(null);
        }
    }, [sshClient, terminal, terminalOptions, onError]);
    // Set up terminal event handlers
    useEffect(() => {
        if (!terminal)
            return;
        const handleData = (data) => {
            onData === null || onData === void 0 ? void 0 : onData(data);
        };
        const handleResize = (cols, rows) => {
            setDimensions({ cols, rows });
            onResize === null || onResize === void 0 ? void 0 : onResize(cols, rows);
        };
        const handleKey = (key, event) => {
            onKey === null || onKey === void 0 ? void 0 : onKey(key, event);
        };
        const handleSelection = (text) => {
            onSelection === null || onSelection === void 0 ? void 0 : onSelection(text);
        };
        const handleTitle = (title) => {
            onTitle === null || onTitle === void 0 ? void 0 : onTitle(title);
        };
        const handleBell = () => {
            onBell === null || onBell === void 0 ? void 0 : onBell();
        };
        const handleError = (err) => {
            setError(err);
            onError === null || onError === void 0 ? void 0 : onError(err);
        };
        terminal.on('data', handleData);
        terminal.on('resize', handleResize);
        terminal.on('key', handleKey);
        terminal.on('selection', handleSelection);
        terminal.on('title', handleTitle);
        terminal.on('bell', handleBell);
        terminal.on('error', handleError);
        return () => {
            terminal.removeListener('data', handleData);
            terminal.removeListener('resize', handleResize);
            terminal.removeListener('key', handleKey);
            terminal.removeListener('selection', handleSelection);
            terminal.removeListener('title', handleTitle);
            terminal.removeListener('bell', handleBell);
            terminal.removeListener('error', handleError);
        };
    }, [terminal, onData, onResize, onKey, onSelection, onTitle, onBell, onError]);
    // Attach terminal to container
    const attach = useCallback((container) => {
        if (!terminal) {
            throw createSSHError('Terminal not initialized', 'TERMINAL_NOT_INITIALIZED');
        }
        try {
            terminal.attach(container);
            containerRef.current = container;
            setIsAttached(true);
            setError(null);
            // Set up auto-resize if enabled
            if (autoResize && window.ResizeObserver) {
                resizeObserverRef.current = new ResizeObserver(() => {
                    setTimeout(() => {
                        terminal.fit();
                    }, 100);
                });
                resizeObserverRef.current.observe(container);
            }
            // Get initial dimensions
            const dims = terminal.getDimensions();
            setDimensions(dims);
        }
        catch (err) {
            const attachError = createSSHError(`Failed to attach terminal: ${err}`, 'TERMINAL_ATTACH_ERROR');
            setError(attachError);
            throw attachError;
        }
    }, [terminal, autoResize]);
    // Detach terminal from container
    const detach = useCallback(() => {
        if (!terminal)
            return;
        try {
            if (resizeObserverRef.current) {
                resizeObserverRef.current.disconnect();
                resizeObserverRef.current = null;
            }
            terminal.detach();
            containerRef.current = null;
            setIsAttached(false);
            setIsConnected(false);
            setDimensions(null);
            setError(null);
        }
        catch (err) {
            const detachError = createSSHError(`Failed to detach terminal: ${err}`, 'TERMINAL_DETACH_ERROR');
            setError(detachError);
        }
    }, [terminal]);
    // Connect terminal to SSH shell
    const connect = useCallback(async (terminalConfig) => {
        if (!terminal) {
            throw createSSHError('Terminal not initialized', 'TERMINAL_NOT_INITIALIZED');
        }
        if (!isAttached) {
            throw createSSHError('Terminal not attached to container', 'TERMINAL_NOT_ATTACHED');
        }
        try {
            await terminal.connect(terminalConfig);
            setIsConnected(true);
            setError(null);
        }
        catch (err) {
            const connectError = createSSHError(`Failed to connect terminal: ${err}`, 'TERMINAL_CONNECT_ERROR');
            setError(connectError);
            throw connectError;
        }
    }, [terminal, isAttached]);
    // Disconnect terminal from SSH shell
    const disconnect = useCallback(async () => {
        if (!terminal)
            return;
        try {
            await terminal.disconnect();
            setIsConnected(false);
            setError(null);
        }
        catch (err) {
            const disconnectError = createSSHError(`Failed to disconnect terminal: ${err}`, 'TERMINAL_DISCONNECT_ERROR');
            setError(disconnectError);
        }
    }, [terminal]);
    // Auto-connect when terminal is attached and SSH client is ready
    useEffect(() => {
        if (autoConnect && terminal && isAttached && (sshClient === null || sshClient === void 0 ? void 0 : sshClient.isReady) && !isConnected) {
            connect().catch(() => {
                // Error is already handled in connect function
            });
        }
    }, [autoConnect, terminal, isAttached, sshClient === null || sshClient === void 0 ? void 0 : sshClient.isReady, isConnected, connect]);
    // Terminal operation functions
    const write = useCallback((data) => {
        terminal === null || terminal === void 0 ? void 0 : terminal.write(data);
    }, [terminal]);
    const writeln = useCallback((data) => {
        terminal === null || terminal === void 0 ? void 0 : terminal.writeln(data);
    }, [terminal]);
    const clear = useCallback(() => {
        terminal === null || terminal === void 0 ? void 0 : terminal.clear();
    }, [terminal]);
    const reset = useCallback(() => {
        terminal === null || terminal === void 0 ? void 0 : terminal.reset();
    }, [terminal]);
    const fit = useCallback(() => {
        terminal === null || terminal === void 0 ? void 0 : terminal.fit();
    }, [terminal]);
    const resize = useCallback((cols, rows) => {
        terminal === null || terminal === void 0 ? void 0 : terminal.resize(cols, rows);
    }, [terminal]);
    const focus = useCallback(() => {
        terminal === null || terminal === void 0 ? void 0 : terminal.focus();
    }, [terminal]);
    const getSelection = useCallback(() => {
        return (terminal === null || terminal === void 0 ? void 0 : terminal.getSelection()) || '';
    }, [terminal]);
    const selectAll = useCallback(() => {
        terminal === null || terminal === void 0 ? void 0 : terminal.selectAll();
    }, [terminal]);
    const copySelection = useCallback(async () => {
        if (terminal) {
            await terminal.copySelection();
        }
    }, [terminal]);
    const paste = useCallback(async () => {
        if (terminal) {
            await terminal.paste();
        }
    }, [terminal]);
    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (resizeObserverRef.current) {
                resizeObserverRef.current.disconnect();
            }
            if (terminal) {
                terminal.detach();
            }
        };
    }, [terminal]);
    return {
        terminal,
        isAttached,
        isConnected,
        dimensions,
        attach,
        detach,
        connect,
        disconnect,
        write,
        writeln,
        clear,
        reset,
        fit,
        resize,
        focus,
        getSelection,
        selectAll,
        copySelection,
        paste,
        error
    };
}
//# sourceMappingURL=use-ssh-terminal.js.map