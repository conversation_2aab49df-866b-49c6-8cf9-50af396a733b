{"version": 3, "file": "use-ssh-terminal.js", "sourceRoot": "", "sources": ["../../src/hooks/use-ssh-terminal.ts"], "names": [], "mappings": "AAAA;;GAEG;;;;;;;;;;;;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACjE,OAAO,EAAE,WAAW,EAAsB,MAAM,aAAa,CAAC;AAG9D,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAgD3C,MAAM,UAAU,cAAc,CAC5B,SAA2B,EAC3B,UAAiC,EAAE;IAEnC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAqB,IAAI,CAAC,CAAC;IACnE,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IACtD,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAwC,IAAI,CAAC,CAAC;IAC1F,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAe,IAAI,CAAC,CAAC;IAEvD,MAAM,YAAY,GAAG,MAAM,CAAqB,IAAI,CAAC,CAAC;IACtD,MAAM,iBAAiB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAE9D,MAAM,EACJ,WAAW,GAAG,KAAK,EACnB,UAAU,GAAG,IAAI,EACjB,MAAM,EACN,QAAQ,EACR,KAAK,EACL,WAAW,EACX,OAAO,EACP,MAAM,EACN,OAAO,KAEL,OAAO,EADN,eAAe,UAChB,OAAO,EAXL,2GAWL,CAAU,CAAC;IAEZ,wDAAwD;IACxD,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;gBAChE,WAAW,CAAC,WAAW,CAAC,CAAC;gBACzB,QAAQ,CAAC,IAAI,CAAC,CAAC;YACjB,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,aAAa,GAAG,cAAc,CAClC,8BAA8B,GAAG,EAAE,EACnC,yBAAyB,CAC1B,CAAC;gBACF,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,aAAa,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;aAAM,IAAI,CAAC,SAAS,IAAI,QAAQ,EAAE,CAAC;YAClC,+CAA+C;YAC/C,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,WAAW,CAAC,IAAI,CAAC,CAAC;YAClB,aAAa,CAAC,KAAK,CAAC,CAAC;YACrB,cAAc,CAAC,KAAK,CAAC,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;IAEpD,iCAAiC;IACjC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,MAAM,UAAU,GAAG,CAAC,IAAY,EAAE,EAAE;YAClC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,IAAI,CAAC,CAAC;QACjB,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE;YAClD,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAG,IAAI,EAAE,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC;QAEF,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,KAAoB,EAAE,EAAE;YACtD,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAG,GAAG,EAAE,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,CAAC,IAAY,EAAE,EAAE;YACvC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC;QAEF,MAAM,WAAW,GAAG,CAAC,KAAa,EAAE,EAAE;YACpC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC;QAEF,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,aAAN,MAAM,uBAAN,MAAM,EAAI,CAAC;QACb,CAAC,CAAC;QAEF,MAAM,WAAW,GAAG,CAAC,GAAU,EAAE,EAAE;YACjC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACd,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC;QAEF,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAChC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACpC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC9B,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAC1C,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAClC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAChC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAElC,OAAO,GAAG,EAAE;YACV,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAC5C,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAChD,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC1C,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YACtD,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAC9C,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAC5C,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAChD,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAE/E,+BAA+B;IAC/B,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,SAAsB,EAAE,EAAE;QACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,cAAc,CAAC,0BAA0B,EAAE,0BAA0B,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC;YACH,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3B,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;YACjC,aAAa,CAAC,IAAI,CAAC,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEf,gCAAgC;YAChC,IAAI,UAAU,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxC,iBAAiB,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,GAAG,EAAE;oBAClD,UAAU,CAAC,GAAG,EAAE;wBACd,QAAQ,CAAC,GAAG,EAAE,CAAC;oBACjB,CAAC,EAAE,GAAG,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC;gBACH,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC;YAED,yBAAyB;YACzB,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;YACtC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEtB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,WAAW,GAAG,cAAc,CAChC,8BAA8B,GAAG,EAAE,EACnC,uBAAuB,CACxB,CAAC;YACF,QAAQ,CAAC,WAAW,CAAC,CAAC;YACtB,MAAM,WAAW,CAAC;QACpB,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;IAE3B,iCAAiC;IACjC,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,EAAE;QAC9B,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,CAAC;YACH,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC9B,iBAAiB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;YACnC,CAAC;YAED,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;YAC5B,aAAa,CAAC,KAAK,CAAC,CAAC;YACrB,cAAc,CAAC,KAAK,CAAC,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,WAAW,GAAG,cAAc,CAChC,8BAA8B,GAAG,EAAE,EACnC,uBAAuB,CACxB,CAAC;YACF,QAAQ,CAAC,WAAW,CAAC,CAAC;QACxB,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,gCAAgC;IAChC,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,EAAE,cAAkC,EAAE,EAAE;QACvE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,cAAc,CAAC,0BAA0B,EAAE,0BAA0B,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,cAAc,CAAC,oCAAoC,EAAE,uBAAuB,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACvC,cAAc,CAAC,IAAI,CAAC,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,cAAc,CACjC,+BAA+B,GAAG,EAAE,EACpC,wBAAwB,CACzB,CAAC;YACF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,MAAM,YAAY,CAAC;QACrB,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;IAE3B,qCAAqC;IACrC,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACxC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,CAAC;YACH,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC5B,cAAc,CAAC,KAAK,CAAC,CAAC;YACtB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,eAAe,GAAG,cAAc,CACpC,kCAAkC,GAAG,EAAE,EACvC,2BAA2B,CAC5B,CAAC;YACF,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,iEAAiE;IACjE,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,WAAW,IAAI,QAAQ,IAAI,UAAU,KAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,CAAA,IAAI,CAAC,WAAW,EAAE,CAAC;YAChF,OAAO,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;gBACnB,+CAA+C;YACjD,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IAElF,+BAA+B;IAC/B,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,IAAY,EAAE,EAAE;QACzC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,IAAY,EAAE,EAAE;QAC3C,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;QAC7B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;QAC7B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE;QAC3B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,GAAG,EAAE,CAAC;IAClB,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE;QACxD,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;QAC7B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,OAAO,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,YAAY,EAAE,KAAI,EAAE,CAAC;IACxC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE;QACjC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,SAAS,EAAE,CAAC;IACxB,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC3C,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;QACjC,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACnC,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,qBAAqB;IACrB,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,GAAG,EAAE;YACV,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC9B,iBAAiB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACzC,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACb,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,OAAO;QACL,QAAQ;QACR,UAAU;QACV,WAAW;QACX,UAAU;QACV,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,OAAO;QACP,KAAK;QACL,KAAK;QACL,GAAG;QACH,MAAM;QACN,KAAK;QACL,YAAY;QACZ,SAAS;QACT,aAAa;QACb,KAAK;QACL,KAAK;KACN,CAAC;AACJ,CAAC"}