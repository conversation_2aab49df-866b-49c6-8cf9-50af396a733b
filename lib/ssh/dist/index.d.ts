/**
 * SSH Library - Main exports
 */
export { SSHClient } from './client';
export { SSHTerminal } from './terminal';
export { SSHConnectionManager } from './connection-manager';
export { useSSHConnection } from './hooks/use-ssh-connection';
export { useSSHTerminal } from './hooks/use-ssh-terminal';
export type { SSHConnectionConfig, SSHAuthMethod, SSHPasswordAuth, SSHKeyAuth, SSHAgentAuth, SSHConnectionState, SSHTerminalConfig, SSHCommandOptions, SSHCommandResult, SSHStreamEvents, SSHConnectionEvents, SSHTerminalIntegration, SSHConnectionPoolConfig, SSHConnectionPoolStats, SSHReconnectionConfig, SSHHostKey, SSHKnownHost, SSHFileTransferOptions, SSHFileInfo, SSHEventListener, SSHEventMap, SSHConfigValidationResult, SSHLibraryOptions } from './types';
export { SSHError, SSHConnectionError, SSHAuthenticationError, SSHTimeoutError, SSHCommandError } from './types';
export { createSSHError, isRecoverableError, isAuthenticationError, isConnectionError, getUserFriendlyErrorMessage, getErrorRecoverySuggestions, logSSHError, SSH_ERROR_CODES } from './errors';
export { validateSSHConfig, validateAuthMethod, validateTerminalConfig, validateCommandOptions, sanitizeConfigForLogging } from './validation';
export { SSHSecurityValidator, DEFAULT_SECURITY_POLICY } from './security';
export type { SSHSecurityPolicy } from './security';
export { parseSSHConnectionString, generateKeyFingerprint, validateHostKey, formatConnectionDuration, escapeShellArg, buildShellCommand, parseCommandOutput, isDangerousCommand, sanitizeCommandForLogging, generateSessionId, isValidPort, isLocalhost, getDefaultSSHPort, formatBytes, debounce, createRetryFunction } from './utils';
export type { SSHTerminalOptions, SSHTerminalEvents } from './terminal';
export type { UseSSHConnectionOptions, UseSSHConnectionReturn } from './hooks/use-ssh-connection';
export type { UseSSHTerminalOptions, UseSSHTerminalReturn } from './hooks/use-ssh-terminal';
export type { ConnectionManagerOptions } from './connection-manager';
export type { ConnectConfig, ClientChannel } from 'ssh2';
//# sourceMappingURL=index.d.ts.map