/**
 * SSH Library - Main exports
 */
// Core classes
export { SSHClient } from './client';
export { SSHTerminal } from './terminal';
export { SSHConnectionManager } from './connection-manager';
// React hooks
export { useSSHConnection } from './hooks/use-ssh-connection';
export { useSSHTerminal } from './hooks/use-ssh-terminal';
// Error classes
export { SSHError, SSHConnectionError, SSHAuthenticationError, SSHTimeoutError, SSHCommandError } from './types';
// Error utilities
export { createSSHError, isRecoverableError, isAuthenticationError, isConnectionError, getUserFriendlyErrorMessage, getErrorRecoverySuggestions, logSSHError, SSH_ERROR_CODES } from './errors';
// Validation utilities
export { validateSSHConfig, validateAuthMethod, validateTerminalConfig, validateCommandOptions, sanitizeConfigForLogging } from './validation';
// Security utilities
export { SSHSecurityValidator, DEFAULT_SECURITY_POLICY } from './security';
// Utility functions
export { parseSSHConnectionString, generateKeyFingerprint, validateHostKey, formatConnectionDuration, escapeShellArg, buildShellCommand, parseCommandOutput, isDangerousCommand, sanitizeCommandForLogging, generateSessionId, isValidPort, isLocalhost, getDefaultSSHPort, formatBytes, debounce, createRetryFunction } from './utils';
//# sourceMappingURL=index.js.map