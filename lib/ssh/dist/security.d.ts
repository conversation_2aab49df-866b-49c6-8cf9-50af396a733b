/**
 * SSH Security utilities and validation
 */
import { SSHConnectionConfig, SSHHostKey } from './types';
/**
 * Security policy configuration
 */
export interface SSHSecurityPolicy {
    requireHostKeyVerification?: boolean;
    allowUnknownHosts?: boolean;
    trustedHosts?: string[];
    allowDangerousCommands?: boolean;
    blockedCommands?: string[];
    allowedCommands?: string[];
    maxConnectionTime?: number;
    maxIdleTime?: number;
    allowedPorts?: number[];
    requireStrongPasswords?: boolean;
    minPasswordLength?: number;
    allowPasswordAuth?: boolean;
    allowKeyAuth?: boolean;
    allowAgentAuth?: boolean;
    maxConnectionAttempts?: number;
    connectionAttemptWindow?: number;
}
/**
 * Default security policy
 */
export declare const DEFAULT_SECURITY_POLICY: Required<SSHSecurityPolicy>;
/**
 * Security validator class
 */
export declare class SSHSecurityValidator {
    private policy;
    private connectionAttempts;
    constructor(policy?: SSHSecurityPolicy);
    /**
     * Validate connection configuration against security policy
     */
    validateConnection(config: SSHConnectionConfig): void;
    /**
     * Validate host and port
     */
    private validateHost;
    /**
     * Validate authentication method
     */
    private validateAuth;
    /**
     * Validate password strength
     */
    private validatePassword;
    /**
     * Validate private key
     */
    private validatePrivateKey;
    /**
     * Check rate limiting for connection attempts
     */
    private checkRateLimit;
    /**
     * Validate command before execution
     */
    validateCommand(command: string): void;
    /**
     * Validate host key
     */
    validateHostKey(host: string, port: number, hostKey: SSHHostKey, knownHosts: SSHHostKey[]): void;
    /**
     * Check connection time limits
     */
    checkConnectionTime(connectionStart: Date): void;
    /**
     * Check idle time limits
     */
    checkIdleTime(lastActivity: Date): void;
    /**
     * Clean up old rate limit entries
     */
    cleanupRateLimitEntries(): void;
    /**
     * Update security policy
     */
    updatePolicy(newPolicy: Partial<SSHSecurityPolicy>): void;
    /**
     * Get current security policy
     */
    getPolicy(): Required<SSHSecurityPolicy>;
}
//# sourceMappingURL=security.d.ts.map