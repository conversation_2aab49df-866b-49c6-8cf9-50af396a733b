{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../src/security.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAC1C,OAAO,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,MAAM,SAAS,CAAC;AAiCxE;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAgC;IAClE,0BAA0B,EAAE,IAAI;IAChC,iBAAiB,EAAE,KAAK;IACxB,YAAY,EAAE,EAAE;IAChB,sBAAsB,EAAE,KAAK;IAC7B,eAAe,EAAE;QACf,UAAU;QACV,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;KACT;IACD,eAAe,EAAE,EAAE;IACnB,iBAAiB,EAAE,OAAO,EAAE,SAAS;IACrC,WAAW,EAAE,OAAO,EAAE,aAAa;IACnC,YAAY,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC;IACxB,sBAAsB,EAAE,IAAI;IAC5B,iBAAiB,EAAE,CAAC;IACpB,iBAAiB,EAAE,IAAI;IACvB,YAAY,EAAE,IAAI;IAClB,cAAc,EAAE,IAAI;IACpB,qBAAqB,EAAE,CAAC;IACxB,uBAAuB,EAAE,MAAM,CAAC,YAAY;CAC7C,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAI/B,YAAY,SAA4B,EAAE;QAFlC,uBAAkB,GAAG,IAAI,GAAG,EAAmD,CAAC;QAGtF,IAAI,CAAC,MAAM,mCAAQ,uBAAuB,GAAK,MAAM,CAAE,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,MAA2B;QAC5C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,IAAY,EAAE,IAAa;QAC9C,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,2BAA2B;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1F,MAAM,cAAc,CAClB,QAAQ,UAAU,oCAAoC,EACtD,kBAAkB,CACnB,CAAC;QACJ,CAAC;QAED,4DAA4D;QAC5D,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACpF,MAAM,cAAc,CAClB,QAAQ,IAAI,mCAAmC,EAC/C,kBAAkB,CACnB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,IAAmB;QACtC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,UAAU;gBACb,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBACnC,MAAM,cAAc,CAClB,wDAAwD,EACxD,wBAAwB,CACzB,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrC,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;oBAC9B,MAAM,cAAc,CAClB,mDAAmD,EACnD,mBAAmB,CACpB,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACzC,MAAM;YAER,KAAK,OAAO;gBACV,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;oBAChC,MAAM,cAAc,CAClB,qDAAqD,EACrD,qBAAqB,CACtB,CAAC;gBACJ,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;YACvC,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBACpD,MAAM,cAAc,CAClB,6BAA6B,IAAI,CAAC,MAAM,CAAC,iBAAiB,kBAAkB,EAC5E,oBAAoB,CACrB,CAAC;YACJ,CAAC;YAED,kCAAkC;YAClC,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACvE,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACnD,MAAM,cAAc,CAClB,uDAAuD,EACvD,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,UAAU,GAAG,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE3D,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YAE3F,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,cAAc,CAClB,wFAAwF,EACxF,kCAAkC,CACnC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,UAA2B;QACpD,MAAM,SAAS,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAEtF,uCAAuC;QACvC,IAAI,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,gDAAgD;YAChD,OAAO;QACT,CAAC;QAED,+BAA+B;QAC/B,MAAM,YAAY,GAAG;YACnB,iCAAiC;YACjC,iCAAiC;YACjC,gCAAgC;YAChC,qCAAqC;YACrC,6BAA6B;SAC9B,CAAC;QAEF,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAE/E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,cAAc,CAClB,sCAAsC,EACtC,oBAAoB,CACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,IAAY;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,qCAAqC;QACrC,IAAI,GAAG,GAAG,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACtE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YACxD,MAAM,cAAc,CAClB,mCAAmC,IAAI,oCAAoC,EAC3E,qBAAqB,CACtB,CAAC;QACJ,CAAC;QAED,oBAAoB;QACpB,QAAQ,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAe;QAC7B,MAAM,gBAAgB,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAE5D,yCAAyC;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;YACvG,MAAM,cAAc,CAClB,0CAA0C,gBAAgB,EAAE,EAC5D,iBAAiB,CAClB,CAAC;QACJ,CAAC;QAED,8CAA8C;QAC9C,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC3D,OAAO,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CACxD,CAAC;YAEF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,cAAc,CAClB,4CAA4C,gBAAgB,EAAE,EAC9D,qBAAqB,CACtB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,IAAI,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACvE,MAAM,cAAc,CAClB,2CAA2C,gBAAgB,EAAE,EAC7D,mBAAmB,CACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY,EAAE,IAAY,EAAE,OAAmB,EAAE,UAAwB;QACvF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC;YAC5C,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;QAEhE,2BAA2B;QAC3B,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CACrC,EAAE,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CACnE,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBACnC,MAAM,cAAc,CAClB,oCAAoC,cAAc,sCAAsC,EACxF,kBAAkB,CACnB,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,eAAqB;QACvC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,eAAe,CAAC,OAAO,EAAE,CAAC;QAEjE,IAAI,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YACnD,MAAM,cAAc,CAClB,gCAAgC,EAChC,uBAAuB,CACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,YAAkB;QAC9B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;QAExD,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,cAAc,CAClB,qCAAqC,EACrC,iBAAiB,CAClB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC;YACjE,IAAI,GAAG,GAAG,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;gBACtE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAqC;QAChD,IAAI,CAAC,MAAM,mCAAQ,IAAI,CAAC,MAAM,GAAK,SAAS,CAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,yBAAY,IAAI,CAAC,MAAM,EAAG;IAC5B,CAAC;CACF"}