/**
 * SSH Terminal integration with xterm.js
 */
import { EventEmitter } from 'events';
import { SSHClient } from './client';
import { SSHTerminalConfig } from './types';
export interface SSHTerminalOptions {
    theme?: 'light' | 'dark' | 'auto';
    fontSize?: number;
    fontFamily?: string;
    cursorBlink?: boolean;
    cursorStyle?: 'block' | 'underline' | 'bar';
    scrollback?: number;
    allowTransparency?: boolean;
    bellStyle?: 'none' | 'visual' | 'sound' | 'both';
}
export interface SSHTerminalEvents {
    data: (data: string) => void;
    resize: (cols: number, rows: number) => void;
    key: (key: string, event: KeyboardEvent) => void;
    selection: (text: string) => void;
    title: (title: string) => void;
    bell: () => void;
    error: (error: Error) => void;
}
export declare class SSHTerminal extends EventEmitter {
    private terminal;
    private fitAddon;
    private webLinksAddon;
    private sshClient;
    private shell;
    private container;
    private isAttached;
    private resizeObserver;
    constructor(sshClient: SSHClient, options?: SSHTerminalOptions);
    /**
     * Attach terminal to DOM element
     */
    attach(container: HTMLElement): void;
    /**
     * Detach terminal from DOM
     */
    detach(): void;
    /**
     * Connect to SSH shell
     */
    connect(terminalConfig?: SSHTerminalConfig): Promise<void>;
    /**
     * Disconnect from SSH shell
     */
    disconnect(): Promise<void>;
    /**
     * Write data to terminal
     */
    write(data: string): void;
    /**
     * Write line to terminal
     */
    writeln(data: string): void;
    /**
     * Clear terminal
     */
    clear(): void;
    /**
     * Reset terminal
     */
    reset(): void;
    /**
     * Fit terminal to container
     */
    fit(): void;
    /**
     * Resize terminal
     */
    resize(cols: number, rows: number): void;
    /**
     * Get terminal dimensions
     */
    getDimensions(): {
        cols: number;
        rows: number;
    };
    /**
     * Focus terminal
     */
    focus(): void;
    /**
     * Get selected text
     */
    getSelection(): string;
    /**
     * Select all text
     */
    selectAll(): void;
    /**
     * Copy selection to clipboard
     */
    copySelection(): Promise<void>;
    /**
     * Paste from clipboard
     */
    paste(): Promise<void>;
    /**
     * Set up terminal event handlers
     */
    private setupTerminalEventHandlers;
    /**
     * Set up shell event handlers
     */
    private setupShellEventHandlers;
    /**
     * Send resize signal to shell
     */
    private sendResize;
    /**
     * Set up resize observer for automatic fitting
     */
    private setupResizeObserver;
    /**
     * Get terminal theme based on preference
     */
    private getTerminalTheme;
}
//# sourceMappingURL=terminal.d.ts.map