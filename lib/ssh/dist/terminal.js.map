{"version": 3, "file": "terminal.js", "sourceRoot": "", "sources": ["../src/terminal.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AACxC,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAEvD,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAGtC,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAuB1C,MAAM,OAAO,WAAY,SAAQ,YAAY;IAU3C,YACE,SAAoB,EACpB,UAA8B,EAAE;QAEhC,KAAK,EAAE,CAAC;QATF,UAAK,GAAyB,IAAI,CAAC;QACnC,cAAS,GAAuB,IAAI,CAAC;QACrC,eAAU,GAAG,KAAK,CAAC;QACnB,mBAAc,GAA0B,IAAI,CAAC;QAQnD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC;YAC3B,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC;YACrD,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAChC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,yCAAyC;YAC3E,WAAW,EAAE,OAAO,CAAC,WAAW,KAAK,KAAK;YAC1C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO;YAC3C,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,IAAI;YACtC,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,KAAK;YACrD,oFAAoF;YACpF,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,KAAK;YACnB,eAAe,EAAE,IAAI;YACrB,qBAAqB,EAAE,IAAI;YAC3B,gBAAgB,EAAE,IAAI;SACvB,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;QAEzC,cAAc;QACd,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE5C,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAsB;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,cAAc,CAAC,8BAA8B,EAAE,2BAA2B,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,4BAA4B;QAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,yBAAyB;QACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,iBAAiB;QACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,iBAAoC,EAAE;QAClD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,CAAC,0CAA0C,EAAE,4BAA4B,CAAC,CAAC;QACjG,CAAC;QAED,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,MAAM,mBACV,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EACxB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EACxB,IAAI,EAAE,gBAAgB,IACnB,cAAc,CAClB,CAAC;YAEF,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACtD,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,sBAAsB;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;YAElB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;YACpD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAExB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,CAAC,+BAA+B,KAAK,EAAE,EAAE,2BAA2B,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,CAAC,kCAAkC,KAAK,EAAE,EAAE,8BAA8B,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAY;QAChB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAY;QAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,GAAG;QACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAY,EAAE,IAAY;QAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACxB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,cAAc,CAAC,gCAAgC,KAAK,EAAE,EAAE,iBAAiB,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAClD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,cAAc,CAAC,mCAAmC,KAAK,EAAE,EAAE,iBAAiB,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B;QAChC,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE;YACpC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;YACxC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE;YACnC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,KAAa,EAAE,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QAExB,yBAAyB;QACzB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YACrC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACtC,MAAM,QAAQ,GAAG,cAAc,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,EAAE,aAAa,CAAC,CAAC;YAChF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACvC,6DAA6D;YAC7D,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,GAAG,EAAE;YAC5C,yBAAyB;YACzB,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAgC;QACvD,MAAM,SAAS,GAAG;YAChB,UAAU,EAAE,SAAS;YACrB,UAAU,EAAE,SAAS;YACrB,MAAM,EAAE,SAAS;YACjB,YAAY,EAAE,SAAS;YACvB,SAAS,EAAE,SAAS;YACpB,KAAK,EAAE,SAAS;YAChB,GAAG,EAAE,SAAS;YACd,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,SAAS;YACjB,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,SAAS;YAClB,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,SAAS;YAChB,WAAW,EAAE,SAAS;YACtB,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,SAAS;YACtB,YAAY,EAAE,SAAS;YACvB,UAAU,EAAE,SAAS;YACrB,aAAa,EAAE,SAAS;YACxB,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,SAAS;SACvB,CAAC;QAEF,MAAM,UAAU,GAAG;YACjB,UAAU,EAAE,SAAS;YACrB,UAAU,EAAE,SAAS;YACrB,MAAM,EAAE,SAAS;YACjB,YAAY,EAAE,SAAS;YACvB,SAAS,EAAE,SAAS;YACpB,KAAK,EAAE,SAAS;YAChB,GAAG,EAAE,SAAS;YACd,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,SAAS;YACjB,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,SAAS;YAClB,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,SAAS;YAChB,WAAW,EAAE,SAAS;YACtB,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,SAAS;YACtB,YAAY,EAAE,SAAS;YACvB,UAAU,EAAE,SAAS;YACrB,aAAa,EAAE,SAAS;YACxB,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,SAAS;SACvB,CAAC;QAEF,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;YACrB,iCAAiC;YACjC,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,8BAA8B,CAAC,CAAC,OAAO,CAAC;YACnG,OAAO,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;QAC9C,CAAC;QAED,OAAO,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;IACnD,CAAC;CACF"}