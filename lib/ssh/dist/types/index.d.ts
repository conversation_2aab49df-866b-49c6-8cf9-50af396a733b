/**
 * Core types for SSH library
 */
import type { ConnectConfig } from 'ssh2';
import type { Terminal } from '@xterm/xterm';
export interface SSHPasswordAuth {
    type: 'password';
    username: string;
    password: string;
}
export interface SSHKeyAuth {
    type: 'key';
    username: string;
    privateKey: string | Buffer;
    passphrase?: string;
}
export interface SSHAgentAuth {
    type: 'agent';
    username: string;
    agent?: string;
}
export type SSHAuthMethod = SSHPasswordAuth | SSHKeyAuth | SSHAgentAuth;
export interface SSHConnectionConfig {
    host: string;
    port?: number;
    auth: SSHAuthMethod;
    timeout?: number;
    keepaliveInterval?: number;
    keepaliveCountMax?: number;
    readyTimeout?: number;
    algorithms?: ConnectConfig['algorithms'];
    hostVerifier?: (keyHash: string, callback: (valid: boolean) => void) => void;
    debug?: (message: string) => void;
}
export type SSHConnectionState = 'disconnected' | 'connecting' | 'connected' | 'authenticating' | 'ready' | 'error' | 'closed';
export interface SSHTerminalConfig {
    rows?: number;
    cols?: number;
    term?: string;
    env?: Record<string, string>;
    pty?: boolean;
    x11?: boolean;
}
export interface SSHCommandOptions {
    timeout?: number;
    env?: Record<string, string>;
    pty?: boolean;
    x11?: boolean;
}
export interface SSHCommandResult {
    stdout: string;
    stderr: string;
    exitCode: number | null;
    signal?: string;
    success: boolean;
    duration: number;
}
export interface SSHStreamEvents {
    data: (chunk: Buffer) => void;
    error: (error: Error) => void;
    close: (code?: number, signal?: string) => void;
    end: () => void;
}
export interface SSHConnectionEvents {
    connect: () => void;
    ready: () => void;
    error: (error: Error) => void;
    close: () => void;
    end: () => void;
    timeout: () => void;
    keyboard: (name: string, ctrl: boolean, meta: boolean, shift: boolean, cmd: boolean) => void;
}
export declare class SSHError extends Error {
    code: string;
    details?: any | undefined;
    constructor(message: string, code: string, details?: any | undefined);
}
export declare class SSHConnectionError extends SSHError {
    constructor(message: string, details?: any);
}
export declare class SSHAuthenticationError extends SSHError {
    constructor(message: string, details?: any);
}
export declare class SSHTimeoutError extends SSHError {
    constructor(message: string, details?: any);
}
export declare class SSHCommandError extends SSHError {
    exitCode: number;
    constructor(message: string, exitCode: number, details?: any);
}
export interface SSHTerminalIntegration {
    terminal: Terminal;
    onData?: (data: string) => void;
    onResize?: (cols: number, rows: number) => void;
    onKey?: (key: string, event: KeyboardEvent) => void;
    theme?: 'light' | 'dark' | 'auto';
}
export interface SSHConnectionPoolConfig {
    maxConnections?: number;
    idleTimeout?: number;
    acquireTimeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
}
export interface SSHConnectionPoolStats {
    total: number;
    active: number;
    idle: number;
    pending: number;
}
export interface SSHReconnectionConfig {
    enabled?: boolean;
    maxAttempts?: number;
    initialDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
    jitter?: boolean;
}
export interface SSHHostKey {
    type: string;
    key: string;
    fingerprint: string;
}
export interface SSHKnownHost {
    host: string;
    port?: number;
    hostKey: SSHHostKey;
}
export interface SSHFileTransferOptions {
    preserveTimestamps?: boolean;
    mode?: number;
    overwrite?: boolean;
    createDirectories?: boolean;
}
export interface SSHFileInfo {
    name: string;
    path: string;
    size: number;
    mode: number;
    uid: number;
    gid: number;
    atime: Date;
    mtime: Date;
    isDirectory: boolean;
    isFile: boolean;
    isSymlink: boolean;
}
export type SSHEventListener<T = any> = (data: T) => void;
export type SSHEventMap = Record<string, SSHEventListener[]>;
export interface SSHConfigValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}
export interface SSHLibraryOptions {
    debug?: boolean;
    logLevel?: 'error' | 'warn' | 'info' | 'debug';
    defaultTimeout?: number;
    defaultKeepAlive?: number;
    maxConcurrentConnections?: number;
}
//# sourceMappingURL=index.d.ts.map