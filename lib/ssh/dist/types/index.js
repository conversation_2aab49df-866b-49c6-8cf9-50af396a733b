/**
 * Core types for SSH library
 */
// Error Types
export class SSHError extends Error {
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'SSHError';
    }
}
export class SSHConnectionError extends SSHError {
    constructor(message, details) {
        super(message, 'CONNECTION_ERROR', details);
        this.name = 'SSHConnectionError';
    }
}
export class SSHAuthenticationError extends SSHError {
    constructor(message, details) {
        super(message, 'AUTHENTICATION_ERROR', details);
        this.name = 'SSHAuthenticationError';
    }
}
export class SSHTimeoutError extends SSHError {
    constructor(message, details) {
        super(message, 'TIMEOUT_ERROR', details);
        this.name = 'SSHTimeoutError';
    }
}
export class SSHCommandError extends SSHError {
    constructor(message, exitCode, details) {
        super(message, 'COMMAND_ERROR', details);
        this.exitCode = exitCode;
        this.name = 'SSHCommandError';
    }
}
//# sourceMappingURL=index.js.map