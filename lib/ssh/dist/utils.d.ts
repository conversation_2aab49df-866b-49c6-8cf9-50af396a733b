/**
 * SSH utility functions
 */
import { SSHConnectionConfig, SSHHostKey } from './types';
/**
 * Parse SSH connection string (user@host:port)
 */
export declare function parseSSHConnectionString(connectionString: string): Partial<SSHConnectionConfig>;
/**
 * Generate SSH key fingerprint
 */
export declare function generateKeyFingerprint(publicKey: string, algorithm?: 'md5' | 'sha256'): string;
/**
 * Validate SSH host key against known hosts
 */
export declare function validateHostKey(host: string, port: number, hostKey: SSHHostKey, knownHosts: SSHHostKey[]): {
    valid: boolean;
    reason?: string;
};
/**
 * Format connection duration
 */
export declare function formatConnectionDuration(startTime: Date, endTime?: Date): string;
/**
 * Escape shell command arguments
 */
export declare function escapeShellArg(arg: string): string;
/**
 * Build shell command from command and arguments
 */
export declare function buildShellCommand(command: string, args?: string[]): string;
/**
 * Parse command output for common patterns
 */
export declare function parseCommandOutput(output: string): {
    lines: string[];
    exitCode?: number;
    hasError: boolean;
    errorLines: string[];
};
/**
 * Check if command is potentially dangerous
 */
export declare function isDangerousCommand(command: string): boolean;
/**
 * Sanitize command for logging
 */
export declare function sanitizeCommandForLogging(command: string): string;
/**
 * Generate unique session ID
 */
export declare function generateSessionId(): string;
/**
 * Check if port is in valid range
 */
export declare function isValidPort(port: number): boolean;
/**
 * Check if hostname is localhost
 */
export declare function isLocalhost(hostname: string): boolean;
/**
 * Get default SSH port
 */
export declare function getDefaultSSHPort(): number;
/**
 * Format bytes to human readable string
 */
export declare function formatBytes(bytes: number): string;
/**
 * Debounce function for terminal resize events
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Create retry function with exponential backoff
 */
export declare function createRetryFunction<T>(fn: () => Promise<T>, maxAttempts?: number, baseDelay?: number, backoffFactor?: number): () => Promise<T>;
//# sourceMappingURL=utils.d.ts.map