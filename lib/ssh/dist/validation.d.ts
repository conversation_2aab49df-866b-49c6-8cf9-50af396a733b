/**
 * SSH Configuration validation utilities
 */
import { SSHConnectionConfig, SSHAuthMethod, SSHConfigValidationResult, SSHTerminalConfig, SSHCommandOptions } from './types';
/**
 * Validate SSH connection configuration
 */
export declare function validateSSHConfig(config: SSHConnectionConfig): SSHConfigValidationResult;
/**
 * Validate authentication method
 */
export declare function validateAuthMethod(auth: SSHAuthMethod): SSHConfigValidationResult;
/**
 * Validate terminal configuration
 */
export declare function validateTerminalConfig(config: SSHTerminalConfig): SSHConfigValidationResult;
/**
 * Validate command options
 */
export declare function validateCommandOptions(options: SSHCommandOptions): SSHConfigValidationResult;
/**
 * Sanitize configuration for logging (remove sensitive data)
 */
export declare function sanitizeConfigForLogging(config: SSHConnectionConfig): Partial<SSHConnectionConfig>;
//# sourceMappingURL=validation.d.ts.map