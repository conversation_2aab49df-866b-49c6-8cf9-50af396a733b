#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/bin/node_modules:/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/node_modules:/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/esbuild@0.21.5/node_modules:/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/bin/node_modules:/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/node_modules:/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/esbuild@0.21.5/node_modules:/home/<USER>/Documents/Development/vk/vibe-kraft/lib/ssh/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/esbuild" "$@"
else
  exec node  "$basedir/../../bin/esbuild" "$@"
fi
