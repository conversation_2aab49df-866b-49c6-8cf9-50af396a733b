{"name": "vibe-kraft", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3080", "build": "next build", "start": "next start", "lint": "next lint", "seed": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "1.2.22", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.1.1", "@leaningtech/cheerpx": "1.1.5", "@monaco-editor/react": "^4.7.0", "@prisma/client": "^6.11.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "ai": "^4.3.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "prisma": "^6.11.0", "puppeteer-core": "^24.12.0", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.0.2", "sonner": "^2.0.5", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "packageManager": "pnpm@10.12.4"}